import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { OUTPUT_DIR, brotliSize, chunkSizeWarningLimit, terserOptions, rollupOptions } from './build/constant'
import viteCompression from 'vite-plugin-compression'
import { viteMockServe } from 'vite-plugin-mock'
import monacoEditorPlugin from 'vite-plugin-monaco-editor'

function pathResolve(dir: string) {
  return resolve(process.cwd(), '.', dir)
}

export default ({ mode }) => {
  // 加载当前环境的变量
  const env = loadEnv(mode, process.cwd())

  // 动态设置 base
  const isProduction = mode === 'production'
  const base = isProduction ? './' : '/'

  // 动态代理配置
  const proxy = {}
  if (env.VITE_PROXY_PREFIX && env.VITE_DEV_PATH) {
    proxy[env.VITE_PROXY_PREFIX] = {
      target: env.VITE_DEV_PATH,
      changeOrigin: true,
      rewrite: (path) => path.replace(new RegExp(`^${env.VITE_PROXY_PREFIX}`), '')
    }
  }

  // 是否启用 Mock
  const isMockEnabled = env.VITE_USE_MOCK === 'true'

  return defineConfig({
    base,
    resolve: {
      alias: [
        { find: /\/#\//, replacement: pathResolve('types') },
        { find: '@', replacement: pathResolve('src') },
        { find: 'vue-i18n', replacement: 'vue-i18n/dist/vue-i18n.cjs.js' }
      ],
      dedupe: ['vue']
    },
    css: {
      preprocessorOptions: {
        scss: {
          javascriptEnabled: true,
          additionalData: `@import "src/styles/common/style.scss";`
        }
      }
    },
    server: {
      host: true,
      open: true,
      port: Number(env.VITE_DEV_PORT) || 3000,
      https: false,
      strictPort: false,
      proxy: proxy
    },
    define: {
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: 'true'
    },
    plugins: [
      vue({
        template: {
          compilerOptions: {
            isCustomElement: tag => tag.startsWith('iconify-icon')
          }
        }
      }),
      monacoEditorPlugin({
        languageWorkers: ['editorWorkerService', 'typescript', 'json', 'html']
      }),
      // 条件启用 Mock
      isMockEnabled
          ? viteMockServe({
            mockPath: '/src/api/mock',
            localEnabled: true,
            prodEnabled: false,
            supportTs: true,
            watchFiles: true
          })
          : null,
      // 压缩（生产环境启用）
      isProduction
          ? viteCompression({
            verbose: true,
            disable: false,
            threshold: 10240,
            algorithm: 'gzip',
            ext: '.gz'
          })
          : null
    ].filter(Boolean), // 过滤 null 插件
    build: {
      target: 'es2020',
      outDir: env.VITE_OUTPUT_DIR || OUTPUT_DIR,
      // minify: 'terser',
      // terserOptions: terserOptions,
      rollupOptions: rollupOptions,
      reportCompressedSize: brotliSize,
      chunkSizeWarningLimit: chunkSizeWarningLimit
    }
  })
}
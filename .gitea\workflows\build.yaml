name: MakeGoViewImageAndPush
on:
  push:
    tags:
      - v*

jobs:
  build-goview:
    runs-on: ubuntu-latest
    container:
      image: catthehacker/ubuntu:act-latest
    env:
      DOCKER_ORG: git.978543210.com/iot-rd
      IMAGE_NAME: ts-iot-go-view
      DOCKER_LATEST: latest
    steps:
      - name: Show event info
        run: echo "🎉 GoView build triggered by a ${{ gitea.event_name }} event."

      - name: Show runner OS
        run: echo "🐧 This job is now running on a ${{ runner.os }} server hosted by <PERSON><PERSON><PERSON>!"

      - name: Show branch/tag info
        run: echo "🔎 The name of your branch or tag is ${{ gitea.ref }} and your repository is ${{ gitea.repository }}."

      - name: Checkout
        uses: https://gitea.com/actions/checkout@v4
        with:
          fetch-depth: 0 # all history for all branches and tags

      - name: Verify repository
        run: echo "Repository ${{ gitea.repository }} ready for build"

      - name: Set up QEMU
        uses: https://gitea.com/docker/setup-qemu-action@v3

      - name: Set up Docker BuildX
        uses: https://gitea.com/docker/setup-buildx-action@v3

      - name: Log in to the Container registry
        uses: https://gitea.com/docker/login-action@v3
        with:
          registry: git.978543210.com
          username: ${{ secrets.CI_USER }}
          password: ${{ secrets.CI_W_TOKEN }}

      - name: Check or create repository
        continue-on-error: true
        run: |
          echo "检查镜像仓库是否存在..."
          # 尝试拉取一个不存在的标签来测试仓库是否存在
          docker pull ${{ env.DOCKER_ORG }}/${{ env.IMAGE_NAME }}:nonexistent-tag 2>/dev/null || echo "仓库可能不存在，将在推送时自动创建"

      - name: Get Meta
        id: meta
        shell: bash
        run: |
          echo "REPO_NAME=$(basename ${GITHUB_REPOSITORY})" >> $GITHUB_OUTPUT
          echo "REPO_VERSION=$(git describe --tags --always | sed 's/^v//')" >> $GITHUB_OUTPUT
          VERSION=$(echo "${{ gitea.ref }}" | sed -e 's,.*/\(.*\),\1,')
          case "${{ gitea.ref }}" in
            refs/tags/*) VERSION=$(echo $VERSION | sed -e 's/^v//') ;;
          esac
          if [ "$VERSION" = "main" ]; then VERSION=latest; fi
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT
          echo "BUILD_DATE=$(date -u +'%Y-%m-%dT%H:%M:%SZ')" >> $GITHUB_OUTPUT
          echo "GIT_SHA=$(echo ${GITHUB_SHA} | cut -c1-8)" >> $GITHUB_OUTPUT

      - name: Build Docker image
        run: |
          echo "Building GoView Docker image..."
          docker build \
            -f Deploy/Dockerfile.goview \
            --build-arg NODE_OPTIONS=--max-old-space-size=16384 \
            --build-arg VITE_PRO_PATH=/prod-api \
            --build-arg BUILD_DATE="${{ steps.meta.outputs.BUILD_DATE }}" \
            --build-arg GIT_SHA="${{ steps.meta.outputs.GIT_SHA }}" \
            --label "org.opencontainers.image.title=${{ env.IMAGE_NAME }}" \
            --label "org.opencontainers.image.description=TS-IOT GoView Frontend Application" \
            --label "org.opencontainers.image.version=${{ steps.meta.outputs.VERSION }}" \
            --label "org.opencontainers.image.created=${{ steps.meta.outputs.BUILD_DATE }}" \
            --label "org.opencontainers.image.revision=${{ github.sha }}" \
            --label "org.opencontainers.image.source=${{ gitea.server_url }}/${{ gitea.repository }}" \
            -t "${{ env.DOCKER_ORG }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.VERSION }}" \
            -t "${{ env.DOCKER_ORG }}/${{ env.IMAGE_NAME }}:latest" \
            .
          echo "✅ 镜像构建完成"

      - name: Push Docker image
        if: startsWith(gitea.ref, 'refs/tags/')
        run: |
          echo "🚀 推送 GoView Docker 镜像..."
          docker push "${{ env.DOCKER_ORG }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.VERSION }}"
          docker push "${{ env.DOCKER_ORG }}/${{ env.IMAGE_NAME }}:latest"
          echo "✅ 镜像推送完成"

      - name: List built images
        run: |
          echo "📋 本地构建的镜像列表:"
          docker images | grep ${{ env.IMAGE_NAME }} || echo "未找到相关镜像"

      - name: GoView build summary
        shell: bash
        run: |
          echo "## 🎉 GoView 构建完成" >> $GITHUB_STEP_SUMMARY
          echo "- **版本**: ${{ steps.meta.outputs.VERSION }}" >> $GITHUB_STEP_SUMMARY
          echo "- **构建时间**: ${{ steps.meta.outputs.BUILD_DATE }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Git SHA**: ${{ steps.meta.outputs.GIT_SHA }}" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像**: ${{ env.DOCKER_ORG }}/${{ env.IMAGE_NAME }}:${{ steps.meta.outputs.VERSION }}" >> $GITHUB_STEP_SUMMARY
          if [ "${{ startsWith(gitea.ref, 'refs/tags/') }}" = "true" ]; then
            echo "- **状态**: ✅ 已推送到镜像仓库" >> $GITHUB_STEP_SUMMARY
            echo "- **访问地址**: http://localhost:8888" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **状态**: 🔨 仅构建测试（未推送）" >> $GITHUB_STEP_SUMMARY
          fi
          echo "- **部署命令**: \`docker-compose up -d goview\`" >> $GITHUB_STEP_SUMMARY

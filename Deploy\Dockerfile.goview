# GoView 前端应用 Dockerfile
# node 构建阶段
FROM git.978543210.com/iot-rd/node:22.12.0 AS build-stage

WORKDIR /app

# 清理环境
RUN rm -rf node_modules

# 复制项目文件
COPY . ./

# 设置 node 阿里镜像
RUN npm config set registry https://registry.npmmirror.com

# 设置内存限制
ENV NODE_OPTIONS=--max-old-space-size=16384

# 设置 GoView 专用的 API 地址
ENV VITE_PRO_PATH=/prod-api

# 安装依赖和构建
RUN npm cache clean --force && \
    npm install -g cnpm && \
    rm -rf node_modules && \
    rm -rf package-lock.json && \
    cnpm install --force && \
    npm run build:prod

# 构建完成提示
RUN echo "🎉 GoView 编译成功 🎉"

# nginx 部署阶段
FROM git.978543210.com/iot-rd/nginx:1.25.4 AS production-stage

# 复制构建产物到 nginx 目录
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制 GoView 专用的 nginx 配置
COPY --from=build-stage /app/Deploy/nginx.goview.conf /etc/nginx/nginx.conf

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 部署完成提示
RUN echo "🎉 GoView 部署成功 🎉"

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]

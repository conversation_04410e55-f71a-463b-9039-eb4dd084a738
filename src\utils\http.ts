/**
 * * 请求失败统一处理
 */
export const httpErrorHandle = () => {
  // 静默处理错误，不显示错误提示
  console.warn('HTTP请求失败，已静默处理')
}

/**
 * * 获取 API 前缀
 * @description 根据当前环境返回对应的 API 前缀路径
 * - 生产环境：使用 VITE_PRO_PATH 或默认 '/prod-api'
 * - 开发环境：使用 VITE_PROXY_PREFIX 或默认 '/dev-api'
 * @returns {string} API 前缀路径
 */
export const getApiPrefix = () => {
  return import.meta.env.PROD
    ? (import.meta.env.VITE_PRO_PATH || '/prod-api')
    : (import.meta.env.VITE_PROXY_PREFIX || '/dev-api')
}

/**
 * * 获取完整的 API 基础 URL
 * @description 返回包含协议、域名和端口的完整 API 基础 URL
 * @returns {string} 完整的 API 基础 URL
 */
export const getApiBaseUrl = () => {
  const { protocol, hostname, port } = window.location
  const apiPrefix = getApiPrefix()
  return `${protocol}//${hostname}${port ? `:${port}` : ''}${apiPrefix}`
}
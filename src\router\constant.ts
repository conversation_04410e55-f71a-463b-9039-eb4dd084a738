export const ErrorPage404 = () => import('@/views/exception/404.vue');

export const ErrorPage403 = () => import('@/views/exception/403.vue');

export const ErrorPage500 = () => import('@/views/exception/500.vue');

export const RedirectHome = () => import('@/views/redirect/index.vue');

export const RedirectUnPublish = () => import('@/views/redirect/UnPublish.vue');

export const Layout = () => import('@/layout/index.vue');

export const ParentLayout = () => import('@/layout/parentLayout.vue');

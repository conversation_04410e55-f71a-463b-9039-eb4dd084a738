# node 构建
FROM git.978543210.com/iot-rd/node:22.12.0 AS build-stage
WORKDIR /app
RUN rm -rf node_modules
COPY . ./
# 设置 node 阿里镜像
RUN npm config set registry https://registry.npmmirror.com
# 设置--max-old-space-size
ENV NODE_OPTIONS=--max-old-space-size=16384
# 设置API地址
# ENV VITE_PRO_PATH=/prod-api

# 设置阿里镜像、pnpm、依赖、编译
RUN npm cache clean --force && \
    npm install -g cnpm && \
    rm -rf node_modules && \
    cnpm install --force && \
    npm run build:prod
# node部分结束
RUN echo "🎉 编 🎉 译 🎉 成 🎉 功 🎉"
# nginx 部署
FROM git.978543210.com/iot-rd/nginx:1.25.4 AS production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html/dist
COPY --from=build-stage /app/Deploy/nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    find /usr/share/nginx/html/dist -type f \( -name "*.js" -o -name "*.json" \) -exec sed -i \
    -e "s|\$vg_base_url|${VG_BASE_URL}|g" \
    -e "s|\$vg_sub_domain|${VG_SUB_DOMAIN}|g" \
    -e "s|\$vg_default_user|${VG_DEFAULT_USER}|g" \
    -e "s|\$vg_default_password|${VG_DEFAULT_PASSWORD}|g" {} \; && \
    echo "🎉 架 🎉 设 🎉 成 🎉 功 🎉"
CMD nginx -g 'daemon off;'

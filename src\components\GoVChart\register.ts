import { <PERSON><PERSON> } from '@visactor/vchart/esm/core';
import { register<PERSON><PERSON><PERSON><PERSON>, register<PERSON>reaChart, registerLineChart, registerPieChart, registerFunnelChart, registerWordCloudChart, registerScatterChart } from '@visactor/vchart/esm/chart';
import { registerTooltip, registerCartesianCrossHair, registerDiscreteLegend, registerLabel } from '@visactor/vchart/esm/component';
import { registerDomTooltipHandler } from '@visactor/vchart/esm/plugin/components';
import { registerAnimate } from '@visactor/vchart';

export const registerChartsAndComponents = () => {
  VChart.useRegisters([
    // 图表
    registerBarChart,
    registerAreaChart,
    registerLineChart,
    registerPie<PERSON>hart,
    registerScatter<PERSON>hart,
    registerFunnelChart,
    registerWordCloudChart,

    // 组件
    registerTooltip,
    registerDomTooltipHandler,
    registerCartesianCrossHair,
    registerDiscreteLegend,
    registerLabel,

    // 动画
    registerAnimate
  ]);
}


{"values": [{"name": "chevrolet chevelle malibu", "milesPerGallon": 18, "cylinders": 8, "horsepower": 130, "x": 8.369035799207357}, {"name": "buick skylark 320", "milesPerGallon": 15, "cylinders": 8, "horsepower": 165, "x": 7.650029728786544}, {"name": "plymouth satellite", "milesPerGallon": 18, "cylinders": 8, "horsepower": 150, "x": 7.968503221959899}, {"name": "amc rebel sst", "milesPerGallon": 16, "cylinders": 8, "horsepower": 150, "x": 7.8561560094781955}, {"name": "ford torino", "milesPerGallon": 17, "cylinders": 8, "horsepower": 140, "x": 7.864427335458599}, {"name": "ford galaxie 500", "milesPerGallon": 15, "cylinders": 8, "horsepower": 198, "x": 7.795269144672034}, {"name": "chevrolet impala", "milesPerGallon": 14, "cylinders": 8, "horsepower": 220, "x": 8.093918960889592}, {"name": "plymouth fury iii", "milesPerGallon": 14, "cylinders": 8, "horsepower": 215, "x": 8.096790572779447}, {"name": "pontiac catalina", "milesPerGallon": 14, "cylinders": 8, "horsepower": 225, "x": 8.280421990443992}, {"name": "amc ambassador dpl", "milesPerGallon": 15, "cylinders": 8, "horsepower": 190, "x": 7.812477402825842}, {"name": "citroen ds-21 pallas", "milesPerGallon": 0, "cylinders": 4, "horsepower": 115, "x": 4.350254126743262}, {"name": "chevrolet chevelle concours (sw)", "milesPerGallon": 0, "cylinders": 8, "horsepower": 165, "x": 8.336047282481855}, {"name": "ford torino (sw)", "milesPerGallon": 0, "cylinders": 8, "horsepower": 153, "x": 8.302128751564197}, {"name": "plymouth satellite (sw)", "milesPerGallon": 0, "cylinders": 8, "horsepower": 175, "x": 8.061597332557989}, {"name": "amc rebel sst (sw)", "milesPerGallon": 0, "cylinders": 8, "horsepower": 175, "x": 7.664837196380818}, {"name": "dodge challenger se", "milesPerGallon": 15, "cylinders": 8, "horsepower": 170, "x": 8.275147641122178}, {"name": "plymouth 'cuda 340", "milesPerGallon": 14, "cylinders": 8, "horsepower": 160, "x": 8.258530083217627}, {"name": "ford mustang boss 302", "milesPerGallon": 0, "cylinders": 8, "horsepower": 140, "x": 7.645170928170343}, {"name": "chevrolet monte carlo", "milesPerGallon": 15, "cylinders": 8, "horsepower": 150, "x": 7.89039684982018}, {"name": "buick estate wagon (sw)", "milesPerGallon": 14, "cylinders": 8, "horsepower": 225, "x": 7.76227513980753}, {"name": "toyota corona mark ii", "milesPerGallon": 24, "cylinders": 4, "horsepower": 95, "x": 4.240267871288519}, {"name": "plymouth duster", "milesPerGallon": 22, "cylinders": 6, "horsepower": 95, "x": 5.815333283103814}, {"name": "amc hornet", "milesPerGallon": 18, "cylinders": 6, "horsepower": 97, "x": 6.199722230971187}, {"name": "ford maverick", "milesPerGallon": 21, "cylinders": 6, "horsepower": 85, "x": 5.84155599489376}, {"name": "datsun pl510", "milesPerGallon": 27, "cylinders": 4, "horsepower": 88, "x": 3.644534398133687}, {"name": "volkswagen 1131 deluxe sedan", "milesPerGallon": 26, "cylinders": 4, "horsepower": 46, "x": 3.652329499162354}, {"name": "peugeot 504", "milesPerGallon": 25, "cylinders": 4, "horsepower": 87, "x": 3.6921618966402843}, {"name": "audi 100 ls", "milesPerGallon": 24, "cylinders": 4, "horsepower": 90, "x": 4.027217142848164}, {"name": "saab 99e", "milesPerGallon": 25, "cylinders": 4, "horsepower": 95, "x": 3.9415947155959774}, {"name": "bmw 2002", "milesPerGallon": 26, "cylinders": 4, "horsepower": 113, "x": 4.050221722279534}, {"name": "amc gremlin", "milesPerGallon": 21, "cylinders": 6, "horsepower": 90, "x": 6.261381817520874}, {"name": "ford f250", "milesPerGallon": 10, "cylinders": 8, "horsepower": 215, "x": 7.695684729213842}, {"name": "chevy c20", "milesPerGallon": 10, "cylinders": 8, "horsepower": 200, "x": 8.209748747255345}, {"name": "dodge d200", "milesPerGallon": 11, "cylinders": 8, "horsepower": 210, "x": 7.846867016987405}, {"name": "hi 1200d", "milesPerGallon": 9, "cylinders": 8, "horsepower": 193, "x": 7.828846703882448}, {"name": "datsun pl510", "milesPerGallon": 27, "cylinders": 4, "horsepower": 88, "x": 3.859449508889943}, {"name": "chevrolet vega 2300", "milesPerGallon": 28, "cylinders": 4, "horsepower": 90, "x": 3.8718649080786602}, {"name": "toyota corona", "milesPerGallon": 25, "cylinders": 4, "horsepower": 95, "x": 3.6787061470327425}, {"name": "ford pinto", "milesPerGallon": 25, "cylinders": 4, "horsepower": 0, "x": 4.0187293963215724}, {"name": "volkswagen super beetle 117", "milesPerGallon": 0, "cylinders": 4, "horsepower": 48, "x": 3.943328407800161}, {"name": "amc gremlin", "milesPerGallon": 19, "cylinders": 6, "horsepower": 100, "x": 5.999494813289073}, {"name": "plymouth satellite custom", "milesPerGallon": 16, "cylinders": 6, "horsepower": 105, "x": 6.290208822990809}, {"name": "chevrolet chevelle malibu", "milesPerGallon": 17, "cylinders": 6, "horsepower": 100, "x": 5.823164098366052}, {"name": "ford torino 500", "milesPerGallon": 19, "cylinders": 6, "horsepower": 88, "x": 6.116011353863191}, {"name": "amc matador", "milesPerGallon": 18, "cylinders": 6, "horsepower": 100, "x": 5.865571771104295}, {"name": "chevrolet impala", "milesPerGallon": 14, "cylinders": 8, "horsepower": 165, "x": 7.80322981951277}, {"name": "pontiac catalina brougham", "milesPerGallon": 14, "cylinders": 8, "horsepower": 175, "x": 7.712942520435072}, {"name": "ford galaxie 500", "milesPerGallon": 14, "cylinders": 8, "horsepower": 153, "x": 8.030269860470632}, {"name": "plymouth fury iii", "milesPerGallon": 14, "cylinders": 8, "horsepower": 150, "x": 7.8959396584543855}, {"name": "dodge monaco (sw)", "milesPerGallon": 12, "cylinders": 8, "horsepower": 180, "x": 8.15167511088829}, {"name": "ford country squire (sw)", "milesPerGallon": 13, "cylinders": 8, "horsepower": 170, "x": 7.642988316347062}, {"name": "pontiac safari (sw)", "milesPerGallon": 13, "cylinders": 8, "horsepower": 175, "x": 7.837391703481973}, {"name": "amc hornet sportabout (sw)", "milesPerGallon": 18, "cylinders": 6, "horsepower": 110, "x": 6.287237197746149}, {"name": "chevrolet vega (sw)", "milesPerGallon": 22, "cylinders": 4, "horsepower": 72, "x": 4.201240369620046}, {"name": "pontiac firebird", "milesPerGallon": 19, "cylinders": 6, "horsepower": 100, "x": 6.091165935264614}, {"name": "ford mustang", "milesPerGallon": 18, "cylinders": 6, "horsepower": 88, "x": 6.36195718874336}, {"name": "mercury capri 2000", "milesPerGallon": 23, "cylinders": 4, "horsepower": 86, "x": 3.8411851683678155}, {"name": "opel 1900", "milesPerGallon": 28, "cylinders": 4, "horsepower": 90, "x": 4.080202623672469}, {"name": "peugeot 304", "milesPerGallon": 30, "cylinders": 4, "horsepower": 70, "x": 3.8607928613726648}, {"name": "fiat 124b", "milesPerGallon": 30, "cylinders": 4, "horsepower": 76, "x": 3.7969976315564606}, {"name": "toyota corolla 1200", "milesPerGallon": 31, "cylinders": 4, "horsepower": 65, "x": 4.363211116247065}, {"name": "datsun 1200", "milesPerGallon": 35, "cylinders": 4, "horsepower": 69, "x": 4.316312298201062}, {"name": "volkswagen model 111", "milesPerGallon": 27, "cylinders": 4, "horsepower": 60, "x": 3.711432044472396}, {"name": "plymouth cricket", "milesPerGallon": 26, "cylinders": 4, "horsepower": 70, "x": 3.9609923230640787}, {"name": "toyota corona hardtop", "milesPerGallon": 24, "cylinders": 4, "horsepower": 95, "x": 4.263784859264984}, {"name": "dodge colt hardtop", "milesPerGallon": 25, "cylinders": 4, "horsepower": 80, "x": 3.8871854792281146}, {"name": "volkswagen type 3", "milesPerGallon": 23, "cylinders": 4, "horsepower": 54, "x": 3.8360975201282663}, {"name": "chevrolet vega", "milesPerGallon": 20, "cylinders": 4, "horsepower": 90, "x": 4.172708392435074}, {"name": "ford pinto runabout", "milesPerGallon": 21, "cylinders": 4, "horsepower": 86, "x": 3.9673857263272256}, {"name": "chevrolet impala", "milesPerGallon": 13, "cylinders": 8, "horsepower": 165, "x": 7.633164000543357}, {"name": "pontiac catalina", "milesPerGallon": 14, "cylinders": 8, "horsepower": 175, "x": 7.886678205550053}, {"name": "plymouth fury iii", "milesPerGallon": 15, "cylinders": 8, "horsepower": 150, "x": 8.275671856536176}, {"name": "ford galaxie 500", "milesPerGallon": 14, "cylinders": 8, "horsepower": 153, "x": 7.951195410494355}, {"name": "amc ambassador sst", "milesPerGallon": 17, "cylinders": 8, "horsepower": 150, "x": 7.882820449295332}, {"name": "mercury marquis", "milesPerGallon": 11, "cylinders": 8, "horsepower": 208, "x": 7.849390788382628}, {"name": "buick lesabre custom", "milesPerGallon": 13, "cylinders": 8, "horsepower": 155, "x": 7.948201490604689}, {"name": "oldsmobile delta 88 royale", "milesPerGallon": 12, "cylinders": 8, "horsepower": 160, "x": 8.210635205663252}, {"name": "chrysler newport royal", "milesPerGallon": 13, "cylinders": 8, "horsepower": 190, "x": 7.641036004573681}, {"name": "mazda rx2 coupe", "milesPerGallon": 19, "cylinders": 3, "horsepower": 97, "x": 2.891821199642409}, {"name": "amc matador (sw)", "milesPerGallon": 15, "cylinders": 8, "horsepower": 150, "x": 8.336629003944669}, {"name": "chevrolet chevelle concours (sw)", "milesPerGallon": 13, "cylinders": 8, "horsepower": 130, "x": 7.9219881116886475}, {"name": "ford gran torino (sw)", "milesPerGallon": 13, "cylinders": 8, "horsepower": 140, "x": 7.8934726533849435}, {"name": "plymouth satellite custom (sw)", "milesPerGallon": 14, "cylinders": 8, "horsepower": 150, "x": 8.057706267563129}, {"name": "volvo 145e (sw)", "milesPerGallon": 18, "cylinders": 4, "horsepower": 112, "x": 3.635290276575149}, {"name": "volkswagen 411 (sw)", "milesPerGallon": 22, "cylinders": 4, "horsepower": 76, "x": 3.8028229183447957}, {"name": "peugeot 504 (sw)", "milesPerGallon": 21, "cylinders": 4, "horsepower": 87, "x": 4.118577741767033}, {"name": "renault 12 (sw)", "milesPerGallon": 26, "cylinders": 4, "horsepower": 69, "x": 4.198450953513762}, {"name": "ford pinto (sw)", "milesPerGallon": 22, "cylinders": 4, "horsepower": 86, "x": 4.329784358153141}, {"name": "datsun 510 (sw)", "milesPerGallon": 28, "cylinders": 4, "horsepower": 92, "x": 3.9435403382828396}, {"name": "toyouta corona mark ii (sw)", "milesPerGallon": 23, "cylinders": 4, "horsepower": 97, "x": 4.35540238583771}, {"name": "dodge colt (sw)", "milesPerGallon": 28, "cylinders": 4, "horsepower": 80, "x": 4.037920811568482}, {"name": "toyota corolla 1600 (sw)", "milesPerGallon": 27, "cylinders": 4, "horsepower": 88, "x": 3.7599084399512606}, {"name": "buick century 350", "milesPerGallon": 13, "cylinders": 8, "horsepower": 175, "x": 8.326745745776993}, {"name": "amc matador", "milesPerGallon": 14, "cylinders": 8, "horsepower": 150, "x": 8.062835335606577}, {"name": "chevrolet malibu", "milesPerGallon": 13, "cylinders": 8, "horsepower": 145, "x": 8.361171737905769}, {"name": "ford gran torino", "milesPerGallon": 14, "cylinders": 8, "horsepower": 137, "x": 7.837847425105593}, {"name": "dodge coronet custom", "milesPerGallon": 15, "cylinders": 8, "horsepower": 150, "x": 7.939600483693272}, {"name": "mercury marquis brougham", "milesPerGallon": 12, "cylinders": 8, "horsepower": 198, "x": 7.644723815901321}, {"name": "chevrolet caprice classic", "milesPerGallon": 13, "cylinders": 8, "horsepower": 150, "x": 7.973438719478234}, {"name": "ford ltd", "milesPerGallon": 13, "cylinders": 8, "horsepower": 158, "x": 7.7188754660438414}, {"name": "plymouth fury gran sedan", "milesPerGallon": 14, "cylinders": 8, "horsepower": 150, "x": 8.11850643108697}, {"name": "chrysler new yorker brougham", "milesPerGallon": 13, "cylinders": 8, "horsepower": 215, "x": 8.205134389394614}, {"name": "buick electra 225 custom", "milesPerGallon": 12, "cylinders": 8, "horsepower": 225, "x": 7.923409436249858}, {"name": "amc ambassador brougham", "milesPerGallon": 13, "cylinders": 8, "horsepower": 175, "x": 8.228936770225731}, {"name": "plymouth valiant", "milesPerGallon": 18, "cylinders": 6, "horsepower": 105, "x": 5.674149489224827}, {"name": "chevrolet nova custom", "milesPerGallon": 16, "cylinders": 6, "horsepower": 100, "x": 6.031504541773996}, {"name": "amc hornet", "milesPerGallon": 18, "cylinders": 6, "horsepower": 100, "x": 5.9503616396021055}, {"name": "ford maverick", "milesPerGallon": 18, "cylinders": 6, "horsepower": 88, "x": 5.976960416158603}, {"name": "plymouth duster", "milesPerGallon": 23, "cylinders": 6, "horsepower": 95, "x": 6.089505720521723}, {"name": "volkswagen super beetle", "milesPerGallon": 26, "cylinders": 4, "horsepower": 46, "x": 3.7303981874150773}, {"name": "chevrolet impala", "milesPerGallon": 11, "cylinders": 8, "horsepower": 150, "x": 7.976213436545324}, {"name": "ford country", "milesPerGallon": 12, "cylinders": 8, "horsepower": 167, "x": 8.268475314508438}, {"name": "plymouth custom suburb", "milesPerGallon": 13, "cylinders": 8, "horsepower": 170, "x": 8.173854895547144}, {"name": "oldsmobile vista cruiser", "milesPerGallon": 12, "cylinders": 8, "horsepower": 180, "x": 7.9615284565949125}, {"name": "amc gremlin", "milesPerGallon": 18, "cylinders": 6, "horsepower": 100, "x": 6.35906036490087}, {"name": "toyota carina", "milesPerGallon": 20, "cylinders": 4, "horsepower": 88, "x": 3.6830139107575413}, {"name": "chevrolet vega", "milesPerGallon": 21, "cylinders": 4, "horsepower": 72, "x": 3.777266668178489}, {"name": "datsun 610", "milesPerGallon": 22, "cylinders": 4, "horsepower": 94, "x": 3.8773818726423626}, {"name": "maxda rx3", "milesPerGallon": 18, "cylinders": 3, "horsepower": 90, "x": 3.0856590246125553}, {"name": "ford pinto", "milesPerGallon": 19, "cylinders": 4, "horsepower": 85, "x": 4.145398212616298}, {"name": "mercury capri v6", "milesPerGallon": 21, "cylinders": 6, "horsepower": 107, "x": 5.801479613628054}, {"name": "fiat 124 sport coupe", "milesPerGallon": 26, "cylinders": 4, "horsepower": 90, "x": 4.204130436361175}, {"name": "chevrolet monte carlo s", "milesPerGallon": 15, "cylinders": 8, "horsepower": 145, "x": 7.789089747321902}, {"name": "pontiac grand prix", "milesPerGallon": 16, "cylinders": 8, "horsepower": 230, "x": 8.368666947054}, {"name": "fiat 128", "milesPerGallon": 29, "cylinders": 4, "horsepower": 49, "x": 3.9641028087660763}, {"name": "opel manta", "milesPerGallon": 24, "cylinders": 4, "horsepower": 75, "x": 4.194627460567969}, {"name": "audi 100ls", "milesPerGallon": 20, "cylinders": 4, "horsepower": 91, "x": 3.7428576589745863}, {"name": "volvo 144ea", "milesPerGallon": 19, "cylinders": 4, "horsepower": 112, "x": 3.8261790588724116}, {"name": "dodge dart custom", "milesPerGallon": 15, "cylinders": 8, "horsepower": 150, "x": 8.261908833153957}, {"name": "saab 99le", "milesPerGallon": 24, "cylinders": 4, "horsepower": 110, "x": 3.8923861779764803}, {"name": "toyota mark ii", "milesPerGallon": 20, "cylinders": 6, "horsepower": 122, "x": 6.023305090318881}, {"name": "oldsmobile omega", "milesPerGallon": 11, "cylinders": 8, "horsepower": 180, "x": 7.916533566730453}, {"name": "plymouth duster", "milesPerGallon": 20, "cylinders": 6, "horsepower": 95, "x": 5.815183875829537}, {"name": "ford maverick", "milesPerGallon": 21, "cylinders": 6, "horsepower": 0, "x": 6.087079591635942}, {"name": "amc hornet", "milesPerGallon": 19, "cylinders": 6, "horsepower": 100, "x": 6.132976630768644}, {"name": "chevrolet nova", "milesPerGallon": 15, "cylinders": 6, "horsepower": 100, "x": 5.77587112911776}, {"name": "datsun b210", "milesPerGallon": 31, "cylinders": 4, "horsepower": 67, "x": 3.7957431353944884}, {"name": "ford pinto", "milesPerGallon": 26, "cylinders": 4, "horsepower": 80, "x": 4.231979588309825}, {"name": "toyota corolla 1200", "milesPerGallon": 32, "cylinders": 4, "horsepower": 65, "x": 3.7452757830244146}, {"name": "chevrolet vega", "milesPerGallon": 25, "cylinders": 4, "horsepower": 75, "x": 4.36199952209112}, {"name": "chevrolet chevelle malibu classic", "milesPerGallon": 16, "cylinders": 6, "horsepower": 100, "x": 6.132102235992026}, {"name": "amc matador", "milesPerGallon": 16, "cylinders": 6, "horsepower": 110, "x": 6.244358407543402}, {"name": "plymouth satellite sebring", "milesPerGallon": 18, "cylinders": 6, "horsepower": 105, "x": 5.9669018127491755}, {"name": "ford gran torino", "milesPerGallon": 16, "cylinders": 8, "horsepower": 140, "x": 7.8345110901907615}, {"name": "buick century luxus (sw)", "milesPerGallon": 13, "cylinders": 8, "horsepower": 150, "x": 8.243711758447686}, {"name": "dodge coronet custom (sw)", "milesPerGallon": 14, "cylinders": 8, "horsepower": 150, "x": 7.6391122249741805}, {"name": "ford gran torino (sw)", "milesPerGallon": 14, "cylinders": 8, "horsepower": 140, "x": 8.149220531713828}, {"name": "amc matador (sw)", "milesPerGallon": 14, "cylinders": 8, "horsepower": 150, "x": 8.257730279738169}, {"name": "audi fox", "milesPerGallon": 29, "cylinders": 4, "horsepower": 83, "x": 3.7204948906649604}, {"name": "volkswagen dasher", "milesPerGallon": 26, "cylinders": 4, "horsepower": 67, "x": 4.044604283879304}, {"name": "opel manta", "milesPerGallon": 26, "cylinders": 4, "horsepower": 78, "x": 4.1329159747108735}, {"name": "toyota corona", "milesPerGallon": 31, "cylinders": 4, "horsepower": 52, "x": 3.763628636444657}, {"name": "datsun 710", "milesPerGallon": 32, "cylinders": 4, "horsepower": 61, "x": 4.108614905428027}, {"name": "dodge colt", "milesPerGallon": 28, "cylinders": 4, "horsepower": 75, "x": 3.829343093554908}, {"name": "fiat 128", "milesPerGallon": 24, "cylinders": 4, "horsepower": 75, "x": 3.992179538347937}, {"name": "fiat 124 tc", "milesPerGallon": 26, "cylinders": 4, "horsepower": 75, "x": 4.021045930602439}, {"name": "honda civic", "milesPerGallon": 24, "cylinders": 4, "horsepower": 97, "x": 4.01260776755379}, {"name": "subaru", "milesPerGallon": 26, "cylinders": 4, "horsepower": 93, "x": 4.080818944944528}, {"name": "fiat x1.9", "milesPerGallon": 31, "cylinders": 4, "horsepower": 67, "x": 4.081015454309478}, {"name": "plymouth valiant custom", "milesPerGallon": 19, "cylinders": 6, "horsepower": 95, "x": 5.7450311922420685}, {"name": "chevrolet nova", "milesPerGallon": 18, "cylinders": 6, "horsepower": 105, "x": 5.810482491296187}, {"name": "mercury monarch", "milesPerGallon": 15, "cylinders": 6, "horsepower": 72, "x": 5.911705207459032}, {"name": "ford maverick", "milesPerGallon": 15, "cylinders": 6, "horsepower": 72, "x": 6.033276990213819}, {"name": "pontiac catalina", "milesPerGallon": 16, "cylinders": 8, "horsepower": 170, "x": 7.718068536346503}, {"name": "chevrolet bel air", "milesPerGallon": 15, "cylinders": 8, "horsepower": 145, "x": 8.317758125572004}, {"name": "plymouth grand fury", "milesPerGallon": 16, "cylinders": 8, "horsepower": 150, "x": 7.7703271590264125}, {"name": "ford ltd", "milesPerGallon": 14, "cylinders": 8, "horsepower": 148, "x": 7.786061816063091}, {"name": "buick century", "milesPerGallon": 17, "cylinders": 6, "horsepower": 110, "x": 5.687539285829092}, {"name": "chevroelt chevelle malibu", "milesPerGallon": 16, "cylinders": 6, "horsepower": 105, "x": 5.846163100863938}, {"name": "amc matador", "milesPerGallon": 15, "cylinders": 6, "horsepower": 110, "x": 6.149551566435133}, {"name": "plymouth fury", "milesPerGallon": 18, "cylinders": 6, "horsepower": 95, "x": 5.7026529649245346}, {"name": "buick skyhawk", "milesPerGallon": 21, "cylinders": 6, "horsepower": 110, "x": 6.030710322304106}, {"name": "chevrolet monza 2+2", "milesPerGallon": 20, "cylinders": 8, "horsepower": 110, "x": 8.337542628333226}, {"name": "ford mustang ii", "milesPerGallon": 13, "cylinders": 8, "horsepower": 129, "x": 7.726720573831461}, {"name": "toyota corolla", "milesPerGallon": 29, "cylinders": 4, "horsepower": 75, "x": 3.899423379967887}, {"name": "ford pinto", "milesPerGallon": 23, "cylinders": 4, "horsepower": 83, "x": 3.981411220984986}, {"name": "amc gremlin", "milesPerGallon": 20, "cylinders": 6, "horsepower": 100, "x": 6.220606678835218}, {"name": "pontiac astro", "milesPerGallon": 23, "cylinders": 4, "horsepower": 78, "x": 3.921476707741298}, {"name": "toyota corona", "milesPerGallon": 24, "cylinders": 4, "horsepower": 96, "x": 3.929493199649912}, {"name": "volkswagen dasher", "milesPerGallon": 25, "cylinders": 4, "horsepower": 71, "x": 3.967731010447716}, {"name": "datsun 710", "milesPerGallon": 24, "cylinders": 4, "horsepower": 97, "x": 3.6434570923586773}, {"name": "ford pinto", "milesPerGallon": 18, "cylinders": 6, "horsepower": 97, "x": 5.657146585274514}, {"name": "volkswagen rabbit", "milesPerGallon": 29, "cylinders": 4, "horsepower": 70, "x": 4.360660460667372}, {"name": "amc pacer", "milesPerGallon": 19, "cylinders": 6, "horsepower": 90, "x": 6.279042915974931}, {"name": "audi 100ls", "milesPerGallon": 23, "cylinders": 4, "horsepower": 95, "x": 3.729205521651694}, {"name": "peugeot 504", "milesPerGallon": 23, "cylinders": 4, "horsepower": 88, "x": 4.172579879270223}, {"name": "volvo 244dl", "milesPerGallon": 22, "cylinders": 4, "horsepower": 98, "x": 3.6460892812911494}, {"name": "saab 99le", "milesPerGallon": 25, "cylinders": 4, "horsepower": 115, "x": 4.099480817622986}, {"name": "honda civic cvcc", "milesPerGallon": 33, "cylinders": 4, "horsepower": 53, "x": 3.686699184979733}, {"name": "fiat 131", "milesPerGallon": 28, "cylinders": 4, "horsepower": 86, "x": 3.8906568823168275}, {"name": "opel 1900", "milesPerGallon": 25, "cylinders": 4, "horsepower": 81, "x": 4.163213309864342}, {"name": "capri ii", "milesPerGallon": 25, "cylinders": 4, "horsepower": 92, "x": 4.041695420795337}, {"name": "dodge colt", "milesPerGallon": 26, "cylinders": 4, "horsepower": 79, "x": 4.025099995007562}, {"name": "renault 12tl", "milesPerGallon": 27, "cylinders": 4, "horsepower": 83, "x": 3.757853046507196}, {"name": "chevrolet chevelle malibu classic", "milesPerGallon": 17.5, "cylinders": 8, "horsepower": 140, "x": 7.699522918782513}, {"name": "dodge coronet brougham", "milesPerGallon": 16, "cylinders": 8, "horsepower": 150, "x": 7.83205495307041}, {"name": "amc matador", "milesPerGallon": 15.5, "cylinders": 8, "horsepower": 120, "x": 8.048915250032099}, {"name": "ford gran torino", "milesPerGallon": 14.5, "cylinders": 8, "horsepower": 152, "x": 8.011651456551443}, {"name": "plymouth valiant", "milesPerGallon": 22, "cylinders": 6, "horsepower": 100, "x": 5.70198208409958}, {"name": "chevrolet nova", "milesPerGallon": 22, "cylinders": 6, "horsepower": 105, "x": 5.920907645277557}, {"name": "ford maverick", "milesPerGallon": 24, "cylinders": 6, "horsepower": 81, "x": 5.773924675854247}, {"name": "amc hornet", "milesPerGallon": 22.5, "cylinders": 6, "horsepower": 90, "x": 6.34431039995742}, {"name": "chevrolet chevette", "milesPerGallon": 29, "cylinders": 4, "horsepower": 52, "x": 4.277594873300891}, {"name": "chevrolet woody", "milesPerGallon": 24.5, "cylinders": 4, "horsepower": 60, "x": 3.765335591263168}, {"name": "vw rabbit", "milesPerGallon": 29, "cylinders": 4, "horsepower": 70, "x": 4.278770224425459}, {"name": "honda civic", "milesPerGallon": 33, "cylinders": 4, "horsepower": 53, "x": 4.161027157987546}, {"name": "dodge aspen se", "milesPerGallon": 20, "cylinders": 6, "horsepower": 100, "x": 6.152268187897024}, {"name": "ford granada ghia", "milesPerGallon": 18, "cylinders": 6, "horsepower": 78, "x": 5.913324809422544}, {"name": "pontiac ventura sj", "milesPerGallon": 18.5, "cylinders": 6, "horsepower": 110, "x": 5.828381858640106}, {"name": "amc pacer d/l", "milesPerGallon": 17.5, "cylinders": 6, "horsepower": 95, "x": 5.8683320093177365}, {"name": "volkswagen rabbit", "milesPerGallon": 29.5, "cylinders": 4, "horsepower": 71, "x": 3.8792962359719265}, {"name": "datsun b-210", "milesPerGallon": 32, "cylinders": 4, "horsepower": 70, "x": 3.911427860622622}, {"name": "toyota corolla", "milesPerGallon": 28, "cylinders": 4, "horsepower": 75, "x": 3.8125207839889685}, {"name": "ford pinto", "milesPerGallon": 26.5, "cylinders": 4, "horsepower": 72, "x": 3.9382401967362717}, {"name": "volvo 245", "milesPerGallon": 20, "cylinders": 4, "horsepower": 102, "x": 3.9172891932003227}, {"name": "plymouth volare premier v8", "milesPerGallon": 13, "cylinders": 8, "horsepower": 150, "x": 7.730584368976243}, {"name": "peugeot 504", "milesPerGallon": 19, "cylinders": 4, "horsepower": 88, "x": 3.6829066868121823}, {"name": "toyota mark ii", "milesPerGallon": 19, "cylinders": 6, "horsepower": 108, "x": 5.767576399045697}, {"name": "mercedes-benz 280s", "milesPerGallon": 16.5, "cylinders": 6, "horsepower": 120, "x": 5.944834766302336}, {"name": "cadillac seville", "milesPerGallon": 16.5, "cylinders": 8, "horsepower": 180, "x": 7.945298420556104}, {"name": "chevy c10", "milesPerGallon": 13, "cylinders": 8, "horsepower": 145, "x": 7.74325655312382}, {"name": "ford f108", "milesPerGallon": 13, "cylinders": 8, "horsepower": 130, "x": 7.641011022203554}, {"name": "dodge d100", "milesPerGallon": 13, "cylinders": 8, "horsepower": 150, "x": 8.205664448591063}, {"name": "honda Accelerationord cvcc", "milesPerGallon": 31.5, "cylinders": 4, "horsepower": 68, "x": 3.9260572773144036}, {"name": "buick opel isuzu deluxe", "milesPerGallon": 30, "cylinders": 4, "horsepower": 80, "x": 4.234095373803297}, {"name": "renault 5 gtl", "milesPerGallon": 36, "cylinders": 4, "horsepower": 58, "x": 3.915485542037537}, {"name": "plymouth arrow gs", "milesPerGallon": 25.5, "cylinders": 4, "horsepower": 96, "x": 3.711987497789524}, {"name": "datsun f-10 hatchback", "milesPerGallon": 33.5, "cylinders": 4, "horsepower": 70, "x": 4.275556352197315}, {"name": "chevrolet caprice classic", "milesPerGallon": 17.5, "cylinders": 8, "horsepower": 145, "x": 8.179625793921783}, {"name": "oldsmobile cutlass supreme", "milesPerGallon": 17, "cylinders": 8, "horsepower": 110, "x": 8.3516900485701}, {"name": "dodge monaco brougham", "milesPerGallon": 15.5, "cylinders": 8, "horsepower": 145, "x": 7.8297250571128885}, {"name": "mercury cougar brougham", "milesPerGallon": 15, "cylinders": 8, "horsepower": 130, "x": 7.944278879464085}, {"name": "chevrolet concours", "milesPerGallon": 17.5, "cylinders": 6, "horsepower": 110, "x": 6.150894070023309}, {"name": "buick skylark", "milesPerGallon": 20.5, "cylinders": 6, "horsepower": 105, "x": 5.723840786618133}, {"name": "plymouth volare custom", "milesPerGallon": 19, "cylinders": 6, "horsepower": 100, "x": 6.200854899011364}, {"name": "ford granada", "milesPerGallon": 18.5, "cylinders": 6, "horsepower": 98, "x": 5.767027512175655}, {"name": "pontiac grand prix lj", "milesPerGallon": 16, "cylinders": 8, "horsepower": 180, "x": 7.7585751577480035}, {"name": "chevrolet monte carlo landau", "milesPerGallon": 15.5, "cylinders": 8, "horsepower": 170, "x": 8.045977852201142}, {"name": "chrysler cordoba", "milesPerGallon": 15.5, "cylinders": 8, "horsepower": 190, "x": 8.192486708372897}, {"name": "ford thunderbird", "milesPerGallon": 16, "cylinders": 8, "horsepower": 149, "x": 8.201223481449942}, {"name": "volkswagen rabbit custom", "milesPerGallon": 29, "cylinders": 4, "horsepower": 78, "x": 4.057195298815751}, {"name": "pontiac sunbird coupe", "milesPerGallon": 24.5, "cylinders": 4, "horsepower": 88, "x": 4.185384444111024}, {"name": "toyota corolla liftback", "milesPerGallon": 26, "cylinders": 4, "horsepower": 75, "x": 3.7552968862230323}, {"name": "ford mustang ii 2+2", "milesPerGallon": 25.5, "cylinders": 4, "horsepower": 89, "x": 3.803025515743171}, {"name": "chevrolet chevette", "milesPerGallon": 30.5, "cylinders": 4, "horsepower": 63, "x": 3.7005952927648225}, {"name": "dodge colt m/m", "milesPerGallon": 33.5, "cylinders": 4, "horsepower": 83, "x": 4.250604683713029}, {"name": "subaru dl", "milesPerGallon": 30, "cylinders": 4, "horsepower": 67, "x": 4.155205977408221}, {"name": "volkswagen dasher", "milesPerGallon": 30.5, "cylinders": 4, "horsepower": 78, "x": 3.794347823856479}, {"name": "datsun 810", "milesPerGallon": 22, "cylinders": 6, "horsepower": 97, "x": 5.748505859329592}, {"name": "bmw 320i", "milesPerGallon": 21.5, "cylinders": 4, "horsepower": 110, "x": 3.691939355771659}, {"name": "mazda rx-4", "milesPerGallon": 21.5, "cylinders": 3, "horsepower": 110, "x": 2.81777818404469}, {"name": "volkswagen rabbit custom diesel", "milesPerGallon": 43.1, "cylinders": 4, "horsepower": 48, "x": 3.7220555062656944}, {"name": "ford fiesta", "milesPerGallon": 36.1, "cylinders": 4, "horsepower": 66, "x": 3.6682376054304378}, {"name": "mazda glc deluxe", "milesPerGallon": 32.8, "cylinders": 4, "horsepower": 52, "x": 4.012845248275688}, {"name": "datsun b210 gx", "milesPerGallon": 39.4, "cylinders": 4, "horsepower": 70, "x": 3.8904807400804913}, {"name": "honda civic cvcc", "milesPerGallon": 36.1, "cylinders": 4, "horsepower": 60, "x": 4.349667176249244}, {"name": "oldsmobile cutlass salon brougham", "milesPerGallon": 19.9, "cylinders": 8, "horsepower": 110, "x": 8.218646383633754}, {"name": "dodge diplomat", "milesPerGallon": 19.4, "cylinders": 8, "horsepower": 140, "x": 8.061011909911953}, {"name": "mercury monarch ghia", "milesPerGallon": 20.2, "cylinders": 8, "horsepower": 139, "x": 8.09467688137649}, {"name": "pontiac phoenix lj", "milesPerGallon": 19.2, "cylinders": 6, "horsepower": 105, "x": 5.848027241639671}, {"name": "chevrolet malibu", "milesPerGallon": 20.5, "cylinders": 6, "horsepower": 95, "x": 6.1706073883547505}, {"name": "ford fairmont (auto)", "milesPerGallon": 20.2, "cylinders": 6, "horsepower": 85, "x": 5.74555866766249}, {"name": "ford fairmont (man)", "milesPerGallon": 25.1, "cylinders": 4, "horsepower": 88, "x": 4.004711038383785}, {"name": "plymouth volare", "milesPerGallon": 20.5, "cylinders": 6, "horsepower": 100, "x": 5.874273239748711}, {"name": "amc concord", "milesPerGallon": 19.4, "cylinders": 6, "horsepower": 90, "x": 6.3572316328228835}, {"name": "buick century special", "milesPerGallon": 20.6, "cylinders": 6, "horsepower": 105, "x": 6.326290929100672}, {"name": "mercury zephyr", "milesPerGallon": 20.8, "cylinders": 6, "horsepower": 85, "x": 6.090315657803135}, {"name": "dodge aspen", "milesPerGallon": 18.6, "cylinders": 6, "horsepower": 110, "x": 6.09127717604979}, {"name": "amc concord d/l", "milesPerGallon": 18.1, "cylinders": 6, "horsepower": 120, "x": 6.178803092678216}, {"name": "chevrolet monte carlo landau", "milesPerGallon": 19.2, "cylinders": 8, "horsepower": 145, "x": 7.8831426332006025}, {"name": "buick regal sport coupe (turbo)", "milesPerGallon": 17.7, "cylinders": 6, "horsepower": 165, "x": 6.266611150726625}, {"name": "ford futura", "milesPerGallon": 18.1, "cylinders": 8, "horsepower": 139, "x": 7.939586988821181}, {"name": "dodge magnum xe", "milesPerGallon": 17.5, "cylinders": 8, "horsepower": 140, "x": 7.6749842730690325}, {"name": "chevrolet chevette", "milesPerGallon": 30, "cylinders": 4, "horsepower": 68, "x": 4.026094223323545}, {"name": "toyota corona", "milesPerGallon": 27.5, "cylinders": 4, "horsepower": 95, "x": 3.989114871371014}, {"name": "datsun 510", "milesPerGallon": 27.2, "cylinders": 4, "horsepower": 97, "x": 3.919495603121351}, {"name": "dodge omni", "milesPerGallon": 30.9, "cylinders": 4, "horsepower": 75, "x": 3.634310672481856}, {"name": "toyota celica gt liftback", "milesPerGallon": 21.1, "cylinders": 4, "horsepower": 95, "x": 4.2362951679593}, {"name": "plymouth sapporo", "milesPerGallon": 23.2, "cylinders": 4, "horsepower": 105, "x": 4.03418952324368}, {"name": "oldsmobile starfire sx", "milesPerGallon": 23.8, "cylinders": 4, "horsepower": 85, "x": 4.021497535176559}, {"name": "datsun 200-sx", "milesPerGallon": 23.9, "cylinders": 4, "horsepower": 97, "x": 3.6549753868403414}, {"name": "audi 5000", "milesPerGallon": 20.3, "cylinders": 5, "horsepower": 103, "x": 5.1890073392770635}, {"name": "volvo 264gl", "milesPerGallon": 17, "cylinders": 6, "horsepower": 125, "x": 5.981745305757347}, {"name": "saab 99gle", "milesPerGallon": 21.6, "cylinders": 4, "horsepower": 115, "x": 4.065826343090008}, {"name": "peugeot 604sl", "milesPerGallon": 16.2, "cylinders": 6, "horsepower": 133, "x": 5.810027165541005}, {"name": "volkswagen scirocco", "milesPerGallon": 31.5, "cylinders": 4, "horsepower": 71, "x": 4.0664857253021145}, {"name": "honda Accelerationord lx", "milesPerGallon": 29.5, "cylinders": 4, "horsepower": 68, "x": 4.274887214970657}, {"name": "pontiac lemans v6", "milesPerGallon": 21.5, "cylinders": 6, "horsepower": 115, "x": 6.230492727989444}, {"name": "mercury zephyr 6", "milesPerGallon": 19.8, "cylinders": 6, "horsepower": 85, "x": 5.948144807434942}, {"name": "ford fairmont 4", "milesPerGallon": 22.3, "cylinders": 4, "horsepower": 88, "x": 3.849531643650541}, {"name": "amc concord dl 6", "milesPerGallon": 20.2, "cylinders": 6, "horsepower": 90, "x": 5.904143705146815}, {"name": "dodge aspen 6", "milesPerGallon": 20.6, "cylinders": 6, "horsepower": 110, "x": 6.287468249670606}, {"name": "chevrolet caprice classic", "milesPerGallon": 17, "cylinders": 8, "horsepower": 130, "x": 8.006794146856944}, {"name": "ford ltd landau", "milesPerGallon": 17.6, "cylinders": 8, "horsepower": 129, "x": 7.915576220737707}, {"name": "mercury grand marquis", "milesPerGallon": 16.5, "cylinders": 8, "horsepower": 138, "x": 7.836955099770109}, {"name": "dodge st. regis", "milesPerGallon": 18.2, "cylinders": 8, "horsepower": 135, "x": 7.893805841233058}, {"name": "buick estate wagon (sw)", "milesPerGallon": 16.9, "cylinders": 8, "horsepower": 155, "x": 7.7080254609954215}, {"name": "ford country squire (sw)", "milesPerGallon": 15.5, "cylinders": 8, "horsepower": 142, "x": 7.765267134267947}, {"name": "chevrolet malibu classic (sw)", "milesPerGallon": 19.2, "cylinders": 8, "horsepower": 125, "x": 7.638579920680975}, {"name": "chrysler lebaron town @ country (sw)", "milesPerGallon": 18.5, "cylinders": 8, "horsepower": 150, "x": 8.145775610523001}, {"name": "vw rabbit custom", "milesPerGallon": 31.9, "cylinders": 4, "horsepower": 71, "x": 3.9398826540339784}, {"name": "maxda glc deluxe", "milesPerGallon": 34.1, "cylinders": 4, "horsepower": 65, "x": 3.6455722629575598}, {"name": "dodge colt hatchback custom", "milesPerGallon": 35.7, "cylinders": 4, "horsepower": 80, "x": 4.12193427440282}, {"name": "amc spirit dl", "milesPerGallon": 27.4, "cylinders": 4, "horsepower": 80, "x": 3.9716265958716166}, {"name": "mercedes benz 300d", "milesPerGallon": 25.4, "cylinders": 5, "horsepower": 77, "x": 5.068023081278515}, {"name": "cadillac eldorado", "milesPerGallon": 23, "cylinders": 8, "horsepower": 125, "x": 7.6675868574290735}, {"name": "peugeot 504", "milesPerGallon": 27.2, "cylinders": 4, "horsepower": 71, "x": 3.764605555949112}, {"name": "oldsmobile cutlass salon brougham", "milesPerGallon": 23.9, "cylinders": 8, "horsepower": 90, "x": 7.766308790449926}, {"name": "plymouth horizon", "milesPerGallon": 34.2, "cylinders": 4, "horsepower": 70, "x": 4.149160207292371}, {"name": "plymouth horizon tc3", "milesPerGallon": 34.5, "cylinders": 4, "horsepower": 70, "x": 4.229858902045439}, {"name": "datsun 210", "milesPerGallon": 31.8, "cylinders": 4, "horsepower": 65, "x": 4.271534379641972}, {"name": "fiat strada custom", "milesPerGallon": 37.3, "cylinders": 4, "horsepower": 69, "x": 4.035466279399953}, {"name": "buick skylark limited", "milesPerGallon": 28.4, "cylinders": 4, "horsepower": 90, "x": 3.71364184991431}, {"name": "chevrolet citation", "milesPerGallon": 28.8, "cylinders": 6, "horsepower": 115, "x": 5.899054957490579}, {"name": "oldsmobile omega brougham", "milesPerGallon": 26.8, "cylinders": 6, "horsepower": 115, "x": 5.946006433464216}, {"name": "pontiac phoenix", "milesPerGallon": 33.5, "cylinders": 4, "horsepower": 90, "x": 3.7396113356866927}, {"name": "vw rabbit", "milesPerGallon": 41.5, "cylinders": 4, "horsepower": 76, "x": 4.097550325478324}, {"name": "toyota corolla tercel", "milesPerGallon": 38.1, "cylinders": 4, "horsepower": 60, "x": 3.9054338799025206}, {"name": "chevrolet chevette", "milesPerGallon": 32.1, "cylinders": 4, "horsepower": 70, "x": 3.9370906275391677}, {"name": "datsun 310", "milesPerGallon": 37.2, "cylinders": 4, "horsepower": 65, "x": 3.6568656488791245}, {"name": "chevrolet citation", "milesPerGallon": 28, "cylinders": 4, "horsepower": 90, "x": 3.821088266512798}, {"name": "ford fairmont", "milesPerGallon": 26.4, "cylinders": 4, "horsepower": 88, "x": 3.70449435635481}, {"name": "amc concord", "milesPerGallon": 24.3, "cylinders": 4, "horsepower": 90, "x": 4.253594061102482}, {"name": "dodge aspen", "milesPerGallon": 19.1, "cylinders": 6, "horsepower": 90, "x": 6.282076136405616}, {"name": "audi 4000", "milesPerGallon": 34.3, "cylinders": 4, "horsepower": 78, "x": 4.3216508497895045}, {"name": "toyota corona liftback", "milesPerGallon": 29.8, "cylinders": 4, "horsepower": 90, "x": 3.888781518516995}, {"name": "mazda 626", "milesPerGallon": 31.3, "cylinders": 4, "horsepower": 75, "x": 3.8457499134604682}, {"name": "datsun 510 hatchback", "milesPerGallon": 37, "cylinders": 4, "horsepower": 92, "x": 4.0894687976157}, {"name": "toyota corolla", "milesPerGallon": 32.2, "cylinders": 4, "horsepower": 75, "x": 3.9309708244265096}, {"name": "mazda glc", "milesPerGallon": 46.6, "cylinders": 4, "horsepower": 65, "x": 4.254203956657513}, {"name": "dodge colt", "milesPerGallon": 27.9, "cylinders": 4, "horsepower": 105, "x": 4.294719995760485}, {"name": "datsun 210", "milesPerGallon": 40.8, "cylinders": 4, "horsepower": 65, "x": 3.6320715714960423}, {"name": "vw rabbit c (diesel)", "milesPerGallon": 44.3, "cylinders": 4, "horsepower": 48, "x": 4.26867037171047}, {"name": "vw dasher (diesel)", "milesPerGallon": 43.4, "cylinders": 4, "horsepower": 48, "x": 4.093809229966266}, {"name": "audi 5000s (diesel)", "milesPerGallon": 36.4, "cylinders": 5, "horsepower": 67, "x": 5.192007249064847}, {"name": "mercedes-benz 240d", "milesPerGallon": 30, "cylinders": 4, "horsepower": 67, "x": 3.9310267304450868}, {"name": "honda civic 1500 gl", "milesPerGallon": 44.6, "cylinders": 4, "horsepower": 67, "x": 4.203679390882783}, {"name": "renault lecar deluxe", "milesPerGallon": 40.9, "cylinders": 4, "horsepower": 0, "x": 4.071898955902105}, {"name": "subaru dl", "milesPerGallon": 33.8, "cylinders": 4, "horsepower": 67, "x": 4.174518278318266}, {"name": "vokswagen rabbit", "milesPerGallon": 29.8, "cylinders": 4, "horsepower": 62, "x": 3.8658063508922753}, {"name": "datsun 280-zx", "milesPerGallon": 32.7, "cylinders": 6, "horsepower": 132, "x": 5.859511410469771}, {"name": "mazda rx-7 gs", "milesPerGallon": 23.7, "cylinders": 3, "horsepower": 100, "x": 3.2086989971664353}, {"name": "triumph tr7 coupe", "milesPerGallon": 35, "cylinders": 4, "horsepower": 88, "x": 4.227487744729735}, {"name": "ford mustang cobra", "milesPerGallon": 23.6, "cylinders": 4, "horsepower": 0, "x": 4.068513150549568}, {"name": "honda Accelerationord", "milesPerGallon": 32.4, "cylinders": 4, "horsepower": 72, "x": 3.6676526960088136}, {"name": "plymouth reliant", "milesPerGallon": 27.2, "cylinders": 4, "horsepower": 84, "x": 4.132367927382765}, {"name": "buick skylark", "milesPerGallon": 26.6, "cylinders": 4, "horsepower": 84, "x": 3.6896256544908237}, {"name": "dodge aries wagon (sw)", "milesPerGallon": 25.8, "cylinders": 4, "horsepower": 92, "x": 4.050868913470094}, {"name": "chevrolet citation", "milesPerGallon": 23.5, "cylinders": 6, "horsepower": 110, "x": 5.785153063704189}, {"name": "plymouth reliant", "milesPerGallon": 30, "cylinders": 4, "horsepower": 84, "x": 3.815100124717885}, {"name": "toyota starlet", "milesPerGallon": 39.1, "cylinders": 4, "horsepower": 58, "x": 4.1983073095232495}, {"name": "plymouth champ", "milesPerGallon": 39, "cylinders": 4, "horsepower": 64, "x": 4.191097759552514}, {"name": "honda civic 1300", "milesPerGallon": 35.1, "cylinders": 4, "horsepower": 60, "x": 4.131654681105934}, {"name": "subaru", "milesPerGallon": 32.3, "cylinders": 4, "horsepower": 67, "x": 4.025765200961957}, {"name": "datsun 210", "milesPerGallon": 37, "cylinders": 4, "horsepower": 65, "x": 4.035308658219122}, {"name": "toyota tercel", "milesPerGallon": 37.7, "cylinders": 4, "horsepower": 62, "x": 4.095695463627345}, {"name": "mazda glc 4", "milesPerGallon": 34.1, "cylinders": 4, "horsepower": 68, "x": 4.012140452105604}, {"name": "plymouth horizon 4", "milesPerGallon": 34.7, "cylinders": 4, "horsepower": 63, "x": 3.6939202725344633}, {"name": "ford escort 4w", "milesPerGallon": 34.4, "cylinders": 4, "horsepower": 65, "x": 4.0109200831843665}, {"name": "ford escort 2h", "milesPerGallon": 29.9, "cylinders": 4, "horsepower": 65, "x": 4.17830102083104}, {"name": "volkswagen jetta", "milesPerGallon": 33, "cylinders": 4, "horsepower": 74, "x": 4.2853561819076145}, {"name": "renault 18i", "milesPerGallon": 34.5, "cylinders": 4, "horsepower": 0, "x": 4.359020127809212}, {"name": "honda prelude", "milesPerGallon": 33.7, "cylinders": 4, "horsepower": 75, "x": 4.1543248596235935}, {"name": "toyota corolla", "milesPerGallon": 32.4, "cylinders": 4, "horsepower": 75, "x": 3.769115939419314}, {"name": "datsun 200sx", "milesPerGallon": 32.9, "cylinders": 4, "horsepower": 100, "x": 4.295307133805183}, {"name": "mazda 626", "milesPerGallon": 31.6, "cylinders": 4, "horsepower": 74, "x": 3.7903396720903535}, {"name": "peugeot 505s turbo diesel", "milesPerGallon": 28.1, "cylinders": 4, "horsepower": 80, "x": 4.054656115599271}, {"name": "saab 900s", "milesPerGallon": 0, "cylinders": 4, "horsepower": 110, "x": 4.326379710931227}, {"name": "volvo diesel", "milesPerGallon": 30.7, "cylinders": 6, "horsepower": 76, "x": 6.197529382540965}, {"name": "toyota cressida", "milesPerGallon": 25.4, "cylinders": 6, "horsepower": 116, "x": 5.765034468301705}, {"name": "datsun 810 maxima", "milesPerGallon": 24.2, "cylinders": 6, "horsepower": 120, "x": 5.640796772821185}, {"name": "buick century", "milesPerGallon": 22.4, "cylinders": 6, "horsepower": 110, "x": 6.110410054807138}, {"name": "oldsmobile cutlass ls", "milesPerGallon": 26.6, "cylinders": 8, "horsepower": 105, "x": 7.644788144403992}, {"name": "ford granada gl", "milesPerGallon": 20.2, "cylinders": 6, "horsepower": 88, "x": 5.768702707154798}, {"name": "chrysler lebaron salon", "milesPerGallon": 17.6, "cylinders": 6, "horsepower": 85, "x": 6.2313844239860385}, {"name": "chevrolet cavalier", "milesPerGallon": 28, "cylinders": 4, "horsepower": 88, "x": 3.7847688007736187}, {"name": "chevrolet cavalier wagon", "milesPerGallon": 27, "cylinders": 4, "horsepower": 88, "x": 4.243022635523257}, {"name": "chevrolet cavalier 2-door", "milesPerGallon": 34, "cylinders": 4, "horsepower": 88, "x": 4.26214795455118}, {"name": "pontiac j2000 se hatchback", "milesPerGallon": 31, "cylinders": 4, "horsepower": 85, "x": 4.294681781593013}, {"name": "dodge aries se", "milesPerGallon": 29, "cylinders": 4, "horsepower": 84, "x": 4.3612227239547865}, {"name": "pontiac phoenix", "milesPerGallon": 27, "cylinders": 4, "horsepower": 90, "x": 3.987914895181706}, {"name": "ford fairmont futura", "milesPerGallon": 24, "cylinders": 4, "horsepower": 92, "x": 3.9060656314781035}, {"name": "amc concord dl", "milesPerGallon": 23, "cylinders": 4, "horsepower": 0, "x": 3.954666226885773}, {"name": "volkswagen rabbit l", "milesPerGallon": 36, "cylinders": 4, "horsepower": 74, "x": 4.30190674508643}, {"name": "mazda glc custom l", "milesPerGallon": 37, "cylinders": 4, "horsepower": 68, "x": 3.6280848611583063}, {"name": "mazda glc custom", "milesPerGallon": 31, "cylinders": 4, "horsepower": 68, "x": 3.6911419278159396}, {"name": "plymouth horizon miser", "milesPerGallon": 38, "cylinders": 4, "horsepower": 63, "x": 4.0011087888837595}, {"name": "mercury lynx l", "milesPerGallon": 36, "cylinders": 4, "horsepower": 70, "x": 4.183203830552381}, {"name": "nissan stanza xe", "milesPerGallon": 36, "cylinders": 4, "horsepower": 88, "x": 4.2272529260287275}, {"name": "honda Accelerationord", "milesPerGallon": 36, "cylinders": 4, "horsepower": 75, "x": 4.257656005997919}, {"name": "toyota corolla", "milesPerGallon": 34, "cylinders": 4, "horsepower": 70, "x": 4.275568077023029}, {"name": "honda civic", "milesPerGallon": 38, "cylinders": 4, "horsepower": 67, "x": 4.118475374376484}, {"name": "honda civic (auto)", "milesPerGallon": 32, "cylinders": 4, "horsepower": 67, "x": 3.9585950997318085}, {"name": "datsun 310 gx", "milesPerGallon": 38, "cylinders": 4, "horsepower": 67, "x": 3.8232069851508967}, {"name": "buick century limited", "milesPerGallon": 25, "cylinders": 6, "horsepower": 110, "x": 5.752982327423358}, {"name": "oldsmobile cutlass ciera (diesel)", "milesPerGallon": 38, "cylinders": 6, "horsepower": 85, "x": 6.189064501959804}, {"name": "chrysler lebaron medallion", "milesPerGallon": 26, "cylinders": 4, "horsepower": 92, "x": 4.2146513880184235}, {"name": "ford granada l", "milesPerGallon": 22, "cylinders": 6, "horsepower": 112, "x": 6.240598069909455}, {"name": "toyota celica gt", "milesPerGallon": 32, "cylinders": 4, "horsepower": 96, "x": 3.9204248039356773}, {"name": "dodge charger 2.2", "milesPerGallon": 36, "cylinders": 4, "horsepower": 84, "x": 4.150100620678576}, {"name": "chevrolet camaro", "milesPerGallon": 27, "cylinders": 4, "horsepower": 90, "x": 3.728501256342185}, {"name": "ford mustang gl", "milesPerGallon": 27, "cylinders": 4, "horsepower": 86, "x": 4.04286904218814}, {"name": "vw pickup", "milesPerGallon": 44, "cylinders": 4, "horsepower": 52, "x": 3.8382667209637664}, {"name": "dodge rampage", "milesPerGallon": 32, "cylinders": 4, "horsepower": 84, "x": 3.889031792685351}, {"name": "ford ranger", "milesPerGallon": 28, "cylinders": 4, "horsepower": 79, "x": 4.177096129854495}, {"name": "chevy s-10", "milesPerGallon": 31, "cylinders": 4, "horsepower": 82, "x": 3.698366552382896}]}
{"visible": true, "unit": {"visible": true, "text": "", "style": {"text": "", "fontSize": 12, "fill": "#B9B8CE", "fontFamily": "Sim<PERSON>un", "fontWeight": "normal", "angle": 0, "dx": 0, "dy": 0}}, "label": {"visible": true, "style": {"fontSize": 12, "fill": "#B9B8CE", "fontFamily": "Sim<PERSON>un", "fontWeight": "normal", "angle": 0, "dx": 0, "dy": 0}}, "title": {"visible": true, "position": "middle", "angle": 0, "padding": [], "style": {"text": "", "fontSize": 12, "fill": "#B9B8CE", "fontFamily": "Sim<PERSON>un", "fontWeight": "normal", "angle": 0, "dx": 0, "dy": 0}}, "domainLine": {"visible": false, "style": {"lineWidth": 1, "stroke": "#D5D7E2"}}, "grid": {"visible": true, "style": {"lineWidth": 1, "stroke": "#FFFFFF24"}}}
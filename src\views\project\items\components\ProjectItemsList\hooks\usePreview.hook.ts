import {fetchPathByName, getSessionStorage} from '@/utils'
import { PreviewEnum } from '@/enums/pageEnum'
import { StorageEnum } from '@/enums/storageEnum'
import { useChartEditStore } from '@/store/modules/chartEditStore/chartEditStore'
import { setSessionStorage, routerTurnByPath, fetchRouteParamsLocation } from '@/utils'

export const usePreview = () => {
  const chartEditStore = useChartEditStore()

  const preview = (id: string) => {
    // 1. 获取预览路径
    const path = fetchPathByName(PreviewEnum.CHART_PREVIEW_NAME, 'href')
    if (!path) {
      console.warn('未找到预览路径')
      return
    }

    // 2. 获取当前项目数据
    const storageInfo = chartEditStore.getStorageInfo()
    const sessionStorageInfo = getSessionStorage(StorageEnum.GO_CHART_STORAGE_LIST) || []

    // 3. 更新 sessionStorage
    const repeateIndex = sessionStorageInfo.findIndex((e: any) => e.id === id)
    if (repeateIndex !== -1) {
      sessionStorageInfo.splice(repeateIndex, 1, { id, ...storageInfo })
    } else {
      sessionStorageInfo.push({ id, ...storageInfo })
    }
    setSessionStorage(StorageEnum.GO_CHART_STORAGE_LIST, sessionStorageInfo)

    // 4. 跳转预览页
    routerTurnByPath(path, [id], undefined, true)
  }

  return { preview }
}
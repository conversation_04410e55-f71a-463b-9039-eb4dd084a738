import axios, { AxiosResponse, AxiosRequestConfig, Axios, AxiosError, InternalAxiosRequestConfig } from 'axios'
import { ResultEnum } from '@/enums/httpEnum'
import { ErrorPageNameMap } from '@/enums/pageEnum'
import { StorageEnum } from '@/enums/storageEnum'
import { SystemStoreEnum, SystemStoreUserInfoEnum } from '@/store/modules/systemStore/systemStore.d'
import { redirectErrorPage, getLocalStorage, isPreview } from '@/utils'
import { fetchAllowList } from './axios.config'
import includes from 'lodash/includes'

export interface MyResponseType<T> {
  code: ResultEnum
  data: T
  rows?: T           // 新字段（适配后端）
  message: string
}

export interface MyRequestInstance extends Axios {
  <T = any>(config: AxiosRequestConfig): Promise<MyResponseType<T>>
}

const axiosInstance = axios.create({
    baseURL: `${import.meta.env.PROD ? (import.meta.env.VITE_PRO_PATH || '') : (import.meta.env.VITE_PROXY_PREFIX || '')}`,
    timeout: ResultEnum.TIMEOUT
}) as unknown as MyRequestInstance

axiosInstance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 白名单校验
    if (includes(fetchAllowList, config.url)) return config
    // 获取 token
    const info = getLocalStorage(StorageEnum.GO_SYSTEM_STORE)
    // 如果有登录信息，则添加token到请求头
    if (info && info[SystemStoreEnum.USER_INFO]) {
      const userInfo = info[SystemStoreEnum.USER_INFO]
      config.headers[userInfo[SystemStoreUserInfoEnum.TOKEN_NAME] || 'token'] =  userInfo[SystemStoreUserInfoEnum.USER_TOKEN] || ''
    }
    // 移除强制登录检查，允许无token访问
    return config
  },
  (err: AxiosError) => {
    Promise.reject(err)
  }
)

// 响应拦截器
axiosInstance.interceptors.response.use(
  (res: AxiosResponse) => {
    // 预览页面错误不进行处理
    if (isPreview()) {
      return Promise.resolve(res.data)
    }
    const { code } = res.data as { code: number }

    if (code === undefined || code === null) return Promise.resolve(res.data)

    // 成功
    if (code === ResultEnum.SUCCESS) {
      return Promise.resolve(res.data)
    }

    // 登录过期 - 静默处理
    if (code === ResultEnum.TOKEN_OVERDUE) {
      console.warn('Token已过期，已静默处理')
      return Promise.resolve(res.data)
    }

    // 固定错误码重定向
    if (ErrorPageNameMap.get(code)) {
      redirectErrorPage(code)
      return Promise.resolve(res.data)
    }

    // 静默处理错误
    console.warn('API请求错误:', (res.data as any).msg)
    return Promise.resolve(res.data)
  },
  (err: any) => {
    // 静默处理所有HTTP错误
    console.warn('HTTP请求错误:', err.message || err)
    return Promise.resolve({ code: -1, data: null, message: '请求失败' })
  }
)

export default axiosInstance

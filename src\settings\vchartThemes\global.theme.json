{"legends": [{"visible": true, "position": "middle", "orient": "bottom", "item": {"visible": true, "align": "left", "shape": "circle", "label": {"style": {"fontSize": 16, "fill": "#B9B8CE", "fontFamily": "Sim<PERSON>un", "fontWeight": "normal"}}}}], "tooltip": {"visible": true, "style": {"panel": {"padding": {"top": 5, "bottom": 10, "left": 10, "right": 10}, "backgroundColor": "rgba(8, 28, 48, 0.95)", "border": {"color": "#CFCFCF", "width": 0, "radius": 2}, "shadow": {"x": 0, "y": 4, "blur": 12, "spread": 0, "color": "rgba(0, 0, 0, 0.2)"}}, "titleLabel": {"fontSize": 14, "fill": "#FFF", "fontWeight": "bold", "fontFamily": "Sim<PERSON>un", "align": "left", "lineHeight": 18}, "keyLabel": {"fontSize": 12, "fill": "rgba(255,255,255,0.65)", "fontWeight": "normal", "fontFamily": "Sim<PERSON>un", "align": "left", "lineHeight": 18}, "valueLabel": {"fontSize": 12, "fill": "#FFF", "fontWeight": "normal", "fontFamily": "Sim<PERSON>un", "align": "right", "lineHeight": 18}, "shape": {"size": 10, "spacing": 10, "shapeLineWidth": 0}, "spaceRow": 10}}}
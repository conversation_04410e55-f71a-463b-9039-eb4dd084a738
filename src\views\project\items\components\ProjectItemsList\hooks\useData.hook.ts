import { ref, reactive } from 'vue'
import { goDialog, httpErrorHandle, getApiPrefix} from '@/utils'
import { DialogEnum } from '@/enums/pluginEnum'
import { projectListApi, deleteProjectApi, changeProjectReleaseApi } from '@/api/path'
import { Chartype, ChartList } from '../../../index.d'
import { ResultEnum } from '@/enums/httpEnum'

// 数据初始化
export const useDataListInit = () => {
  const loading = ref(true)

  const paginat = reactive({
    // 当前页数
    page: 1,
    // 每页值
    limit: 12,
    // 总数
    count: 10
  })

  const list = ref<ChartList>([])

  // 数据请求
  const fetchList = async () => {
    try {
      loading.value = true
      const res = await projectListApi({
        page: paginat.page,
        limit: paginat.limit
      })

      // 检查响应是否存在
      if (res) {
        // 更宽松的成功条件判断
        if (res.code === 200 || res.code === 0 || res.code === undefined || res.code === null) {
          // 检查数据结构 - 更宽松的数据检查
          res.data = res.rows || res.data || []
          if (res.data) {
            let dataArray = res.data

            // 如果data不是数组，尝试其他可能的数据结构
            if (!Array.isArray(res.data)) {
              const dataObj = res.data as any
              if (dataObj.list && Array.isArray(dataObj.list)) {
                dataArray = dataObj.list
                console.log('使用data.list作为数据源')
              } else if (dataObj.records && Array.isArray(dataObj.records)) {
                dataArray = dataObj.records
                console.log('使用data.records作为数据源')
              } else {
                console.log('尝试将data作为单个项目处理')
                dataArray = [res.data]
              }
            }

            const { count, total } = res as any
            paginat.count = count || total || dataArray.length

            list.value = dataArray.map((e, index) => {
              const { id, projectName, state, createTime, indexImage, createBy } = e
              return {
                id: id,
                title: projectName || `项目${index + 1}`,
                createId: createBy,
                time: createTime,
                image: indexImage ? `${getApiPrefix()}${indexImage.replace(/^\/+/, '/')}` : '',
                release: state !== 0
              }
            })

            loading.value = false
            return
          } else {
            console.error('响应中没有data字段')
          }
        } else {
          console.error('API响应失败，code:', res.code)
        }
      } else {
        console.error('没有收到API响应')
      }
    } catch (error) {
      console.error('请求过程中发生错误:', error)
      // 检查是否是网络连接错误
      if (error && (error as any).message && (error as any).message.includes('Network Error')) {
        console.warn('网络连接失败，将在5秒后使用测试数据')
      }
    } finally {
      // 确保loading状态一定会被重置
      loading.value = false
    }

    console.error('项目列表请求失败，调用错误处理')
    httpErrorHandle()

    // 额外的安全措施：延迟重置loading状态
    setTimeout(() => {
      console.log('延迟重置：确保loading为false')
      loading.value = false
    }, 1000)
  }

  // 修改页数
  const changePage = (_page: number) => {
    paginat.page = _page
    fetchList()
  }

  // 修改大小
  const changeSize = (_size: number) => {
    paginat.limit = _size
    fetchList()
  }

  // 删除处理
  const deleteHandle = (cardData: Chartype) => {
    goDialog({
      type: DialogEnum.DELETE,
      promise: true,
      onPositiveCallback: () =>
          new Promise(res => {
            res(
                deleteProjectApi(cardData.id)
            )
          }),
      promiseResCallback: (res: any) => {
        if (res.code === ResultEnum.SUCCESS) {
          window['$message'].success(window['$t']('global.r_delete_success'))
          fetchList()
          return
        }
        httpErrorHandle()
      }
    })
  }

  // 发布处理
  const releaseHandle = async (cardData: Chartype, index: number) => {
    const { id, release } = cardData
    const res = await changeProjectReleaseApi({
      id: id,
      // [0未发布, 1发布]
      state: !release ? 1 : 0
    })
    if (res && res.code === ResultEnum.SUCCESS) {
      list.value = []
      fetchList()
      // 发布 -> 未发布
      if (release) {
        window['$message'].success(window['$t']('global.r_unpublish_success'))
        return
      }
      // 未发布 -> 发布
      window['$message'].success(window['$t']('global.r_publish_success'))
      return
    }
    httpErrorHandle()
  }

  // 添加测试数据功能
  const setTestData = () => {
    console.log('设置测试数据')
    list.value = [
      {
        id: '1',
        title: '测试项目1',
        createId: '1',
        time: '2024-01-01',
        image: '',
        release: true
      },
      {
        id: '2',
        title: '测试项目2',
        createId: '1',
        time: '2024-01-02',
        image: '',
        release: false
      }
    ]
    paginat.count = 2
    loading.value = false
    console.log('测试数据设置完成，loading:', loading.value)
  }

  // 立即请求
  fetchList()

  // 如果5秒后还在loading，设置测试数据
  setTimeout(() => {
    if (loading.value) {
      console.warn('5秒后仍在loading状态，设置测试数据')
      setTestData()
    }
  }, 5000)

  return {
    loading,
    paginat,
    list,
    fetchList,
    releaseHandle,
    changeSize,
    changePage,
    deleteHandle,
  }
}

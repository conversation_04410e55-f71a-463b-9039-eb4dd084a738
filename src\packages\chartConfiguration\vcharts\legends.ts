export const legendsConfig = {
  // 位置
  orient: [
    {
      label: '顶部',
      value: 'top'
    },
    {
      label: '底部',
      value: 'bottom'
    },
    {
      label: '左侧',
      value: 'left'
    },
    {
      label: '右侧',
      value: 'right'
    }
  ],
  // 对齐方式
  position: [
    {
      label: '起始',
      value: 'start'
    },
    {
      label: '居中',
      value: 'middle'
    },
    {
      label: '末尾',
      value: 'end'
    }
  ],
  // 每一项的图例位置
  align: [
    {
      label: '居左',
      value: 'left'
    },
    {
      label: '居右',
      value: 'right'
    }
  ]
}

export const fontStyleConfig = {
  // 字重
  fontWeight: [
    {
      label: '100',
      value: 100
    },
    {
      label: '200',
      value: 200
    },
    {
      label: '300',
      value: 300
    },
    {
      label: '400',
      value: 400
    },
    {
      label: '500',
      value: 500
    },
    {
      label: '600',
      value: 600
    },
    {
      label: '正常',
      value: "normal"
    },
    {
      label: '加粗',
      value: "bold"
    }
  ],
  fontFamily: [
    {
      label: '宋体',
      value: 'SimSun'
    },
    {
      label: '黑体',
      value: 'SimHei'
    },
    {
      label: '楷体',
      value: '楷体'
    }
  ]
}
